<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Test - 1024px</title>
    <style>
        body {
            font-family: 'Lato', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #FCFAF7;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .about-section {
            background: #F5F1EC;
            padding: 48px 96px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .about-section h2 {
            font-family: 'Raleway', sans-serif;
            font-size: 2.5rem;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 24px;
        }
        
        .about-section p {
            font-family: 'Lato', sans-serif;
            font-size: 1.25rem;
            line-height: 1.75;
            color: #374151;
            margin-bottom: 32px;
            max-width: 768px;
        }
        
        .about-section button {
            background: #F2EBE8;
            color: #1f2937;
            padding: 12px 32px;
            border-radius: 8px;
            border: 1px solid #d1d5db;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .about-section button:hover {
            background: #e8ddd6;
            border-color: #9ca3af;
            transform: scale(1.02);
        }
        
        .carousel-section {
            padding: 24px 96px;
            margin: 20px 0;
        }
        
        .carousel-container {
            overflow: hidden;
            border-radius: 16px;
            padding: 16px;
            background: linear-gradient(-45deg, #fff5f5, #fef7ed, #fefce8, #f0fdf4);
        }
        
        .carousel-track {
            display: flex;
            gap: 20px;
            transition: transform 0.5s ease;
        }
        
        .card {
            flex-shrink: 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border: 1px solid #fed7aa;
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 12px 24px rgba(0,0,0,0.15);
        }
        
        .card-image {
            position: relative;
            aspect-ratio: 1;
            overflow: hidden;
        }
        
        .card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .card:hover .card-image img {
            transform: scale(1.1);
        }
        
        .price-tag {
            position: absolute;
            top: 12px;
            right: 12px;
            background: linear-gradient(to right, #ea580c, #f97316);
            color: white;
            padding: 4px 12px;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .card-content {
            padding: 16px;
        }
        
        .card-title {
            font-family: 'Raleway', sans-serif;
            font-size: 1rem;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .card-description {
            font-family: 'Lato', sans-serif;
            font-size: 0.875rem;
            color: #6b7280;
            line-height: 1.5;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .card-rating {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .stars {
            display: flex;
            color: #fb923c;
        }
        
        .star {
            width: 12px;
            height: 12px;
            fill: currentColor;
        }
        
        .rating-text {
            font-size: 0.75rem;
            color: #6b7280;
        }
        
        .viewport-info {
            background: #e0f2fe;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: center;
        }
        
        /* Responsive breakpoints */
        @media (max-width: 768px) {
            .card { width: calc(100% - 15px); }
            .viewport-info::after { content: " - Mobile (1 card)"; }
        }
        
        @media (min-width: 769px) and (max-width: 1023px) {
            .card { width: calc(50% - 15px); }
            .viewport-info::after { content: " - Tablet (2 cards)"; }
        }
        
        @media (min-width: 1024px) and (max-width: 1280px) {
            .card { width: calc(33.333% - 15px); }
            .viewport-info::after { content: " - Laptop 1024px (3 cards)"; }
        }
        
        @media (min-width: 1281px) {
            .card { width: calc(25% - 15px); }
            .viewport-info::after { content: " - Desktop (4 cards)"; }
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Raleway:wght@400;600;700&family=Lato:wght@300;400;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="test-container">
        <h1>Responsive Design Test</h1>
        <div class="viewport-info">
            <strong>Current Viewport:</strong> <span id="viewport-size"></span>
        </div>
        
        <!-- About Us Section -->
        <div class="about-section">
            <h2>About Us</h2>
            <p>The Daily Grind is more than just a cafe; it's a community hub where coffee lovers and food enthusiasts come together. Our journey began with a simple idea: to create a space where quality coffee meets a warm, inviting atmosphere. Learn more about our mission, values, and the team behind your favorite brews.</p>
            <button>Learn More About Us</button>
        </div>
        
        <!-- Menu Highlights Section -->
        <div class="carousel-section">
            <h2 style="font-family: 'Raleway', sans-serif; font-size: 1.5rem; font-weight: bold; color: #1f2937; margin-bottom: 24px;">Menu Highlights</h2>
            
            <div class="carousel-container">
                <div class="carousel-track">
                    <div class="card">
                        <div class="card-image">
                            <img src="https://images.unsplash.com/photo-1509042239860-f550ce710b93?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Artisan Coffee">
                            <div class="price-tag">₹350</div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Artisan Coffee</h3>
                            <p class="card-description">Our coffee is sourced from the finest beans, roasted to perfection, and crafted with care.</p>
                            <div class="card-rating">
                                <div class="stars">
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                </div>
                                <span class="rating-text">4.8</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-image">
                            <img src="https://images.unsplash.com/photo-1555507036-ab794f4afe5a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Freshly Baked Delights">
                            <div class="price-tag">₹250</div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Freshly Baked Delights</h3>
                            <p class="card-description">Indulge in our daily selection of pastries, made with love and the freshest ingredients.</p>
                            <div class="card-rating">
                                <div class="stars">
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                </div>
                                <span class="rating-text">4.9</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-image">
                            <img src="https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Savory Bites">
                            <div class="price-tag">₹680</div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Savory Bites</h3>
                            <p class="card-description">Enjoy a variety of sandwiches, prepared with quality meats, cheeses, and homemade sauces.</p>
                            <div class="card-rating">
                                <div class="stars">
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                </div>
                                <span class="rating-text">4.7</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-image">
                            <img src="https://images.unsplash.com/photo-1578985545062-69928b1d9587?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Sweet Treats">
                            <div class="price-tag">₹420</div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Sweet Treats</h3>
                            <p class="card-description">Delicious desserts and cakes made fresh daily with premium ingredients.</p>
                            <div class="card-rating">
                                <div class="stars">
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                    <svg class="star" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>
                                </div>
                                <span class="rating-text">4.9</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="viewport-info">
            <strong>Instructions:</strong> Resize your browser window to test different breakpoints. At 1024px, you should see exactly 3 cards in the menu section.
        </div>
    </div>

    <script>
        function updateViewportInfo() {
            const width = window.innerWidth;
            document.getElementById('viewport-size').textContent = width + 'px';
        }
        
        updateViewportInfo();
        window.addEventListener('resize', updateViewportInfo);
    </script>
</body>
</html>
