import { motion } from 'framer-motion';

const OurStoryHero = () => (
  <section className="relative px-8 sm:px-12 md:px-16 lg:px-24 xl:px-32 2xl:px-40 py-12">
    {/* Hero Image */}
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
      className="relative h-64 sm:h-80 md:h-96 lg:h-[28rem] rounded-2xl overflow-hidden mb-12"
    >
      <img
        src="https://images.unsplash.com/photo-1554118811-1e0d58224f24?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2047&q=80"
        alt="Cafe interior with people enjoying coffee"
        className="w-full h-full object-cover"
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
    </motion.div>

    {/* The Beginning Section */}
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.3 }}
      className="max-w-4xl"
    >
      <h2 
        className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-6"
        style={{ fontFamily: 'Raleway, sans-serif' }}
      >
        The Beginning
      </h2>
      
      <p 
        className="text-base sm:text-lg md:text-xl text-gray-700 leading-relaxed"
        style={{ fontFamily: 'Lato, sans-serif' }}
      >
        The Daily Grind was founded in 2010 by Sarah and David, two passionate coffee enthusiasts with a dream of creating a 
        community hub centered around exceptional coffee and delicious food. Their journey began with a small, cozy space in the 
        heart of the city, where they meticulously crafted each cup and dish with love and care.
      </p>
    </motion.div>
  </section>
);

const OurJourney = () => (
  <section className="relative px-8 sm:px-12 md:px-16 lg:px-24 xl:px-32 2xl:px-40 py-12" style={{ backgroundColor: '#F5F1EC' }}>
    <div className="max-w-6xl mx-auto">
      <motion.h2
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-8 text-center"
        style={{ fontFamily: 'Raleway, sans-serif' }}
      >
        Our Journey
      </motion.h2>

      <div className="grid md:grid-cols-2 gap-12 items-center">
        <motion.div
          initial={{ opacity: 0, x: -30 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <h3 
            className="text-xl sm:text-2xl font-semibold text-gray-900 mb-4"
            style={{ fontFamily: 'Raleway, sans-serif' }}
          >
            From Dream to Reality
          </h3>
          <p 
            className="text-gray-700 leading-relaxed mb-6"
            style={{ fontFamily: 'Lato, sans-serif' }}
          >
            What started as a simple dream has grown into a beloved community gathering place. Over the years, we've expanded our 
            menu, refined our craft, and built lasting relationships with our customers who have become like family to us.
          </p>
          <p 
            className="text-gray-700 leading-relaxed"
            style={{ fontFamily: 'Lato, sans-serif' }}
          >
            Every cup we serve and every dish we prepare carries the same passion and dedication that Sarah and David brought 
            to that first small cafe over a decade ago.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: 30 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="relative h-64 sm:h-80 rounded-xl overflow-hidden"
        >
          <img
            src="https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
            alt="Coffee being prepared"
            className="w-full h-full object-cover"
          />
        </motion.div>
      </div>
    </div>
  </section>
);

const OurValues = () => (
  <section className="relative px-8 sm:px-12 md:px-16 lg:px-24 xl:px-32 2xl:px-40 py-12">
    <div className="max-w-6xl mx-auto">
      <motion.h2
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-12 text-center"
        style={{ fontFamily: 'Raleway, sans-serif' }}
      >
        Our Values
      </motion.h2>

      <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
        {[
          {
            title: "Quality First",
            description: "We source the finest ingredients and use traditional methods to ensure every item meets our high standards.",
            icon: "☕"
          },
          {
            title: "Community Focus",
            description: "We believe in creating a space where people can connect, work, and feel at home in our welcoming environment.",
            icon: "🤝"
          },
          {
            title: "Sustainability",
            description: "We're committed to sustainable practices, from sourcing to packaging, to protect our planet for future generations.",
            icon: "🌱"
          }
        ].map((value, index) => (
          <motion.div
            key={value.title}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.2 }}
            className="text-center p-6 rounded-xl"
            style={{ backgroundColor: '#FCFAF7' }}
          >
            <div className="text-4xl mb-4">{value.icon}</div>
            <h3 
              className="text-xl font-semibold text-gray-900 mb-3"
              style={{ fontFamily: 'Raleway, sans-serif' }}
            >
              {value.title}
            </h3>
            <p 
              className="text-gray-700 leading-relaxed"
              style={{ fontFamily: 'Lato, sans-serif' }}
            >
              {value.description}
            </p>
          </motion.div>
        ))}
      </div>
    </div>
  </section>
);

export const OurStoryPage = () => {
  return (
    <div className="min-h-screen pt-20" style={{ backgroundColor: '#faf8f5' }}>
      <OurStoryHero />
      <OurJourney />
      <OurValues />
    </div>
  );
};
